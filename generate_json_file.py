#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON文件生成器
主要功能：创建指定大小的JSON文件
"""

import json
import os
import sys
import random
import string
import logging
from datetime import datetime
from typing import Dict, Any


class JsonFileGenerator:
    """JSON文件生成器类"""
    
    def __init__(self):
        self.setup_logging()
        self.sample_data_templates = [
            {"id": 1, "name": "示例产品", "price": 99.99, "category": "电子产品"},
            {"user_id": 1001, "username": "user001", "email": "<EMAIL>", "active": True},
            {"order_id": "ORD001", "total": 299.99, "status": "completed", "items": []},
            {"brand_id": 1, "brand_name": "示例品牌", "description": "品牌描述信息"}
        ]
    
    def setup_logging(self):
        """设置日志配置"""
        # 创建logger
        self.logger = logging.getLogger('JsonFileGenerator')
        self.logger.setLevel(logging.INFO)
        
        # 如果已经有handler，则不重复添加
        if not self.logger.handlers:
            # 创建文件handler
            log_filename = f'json_generator_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
            file_handler = logging.FileHandler(log_filename, encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            
            # 创建控制台handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 创建formatter
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            # 添加handler到logger
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
            
            self.log(f"日志系统初始化完成，日志文件: {log_filename}")
    
    def log(self, message: str, level: str = 'info'):
        """记录日志的便捷方法
        
        Args:
            message: 日志消息
            level: 日志级别 ('debug', 'info', 'warning', 'error', 'critical')
        """
        level = level.lower()
        if level == 'debug':
            self.logger.debug(message)
        elif level == 'info':
            self.logger.info(message)
        elif level == 'warning':
            self.logger.warning(message)
        elif level == 'error':
            self.logger.error(message)
        elif level == 'critical':
            self.logger.critical(message)
        else:
            self.logger.info(message)
    
    def generate_random_string(self, length: int = 10) -> str:
        """生成随机字符串"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))
    
    def generate_sample_object(self) -> Dict[str, Any]:
        """生成示例JSON对象"""
        template = random.choice(self.sample_data_templates)
        
        # 基于模板创建新对象，添加随机数据
        obj = template.copy()
        
        # 添加一些随机字段来增加数据量
        obj["timestamp"] = f"2024-{random.randint(1, 12):02d}-{random.randint(1, 28):02d}"
        obj["random_id"] = self.generate_random_string(8)
        obj["description"] = self.generate_random_string(50)
        obj["metadata"] = {
            "created_by": self.generate_random_string(8),
            "tags": [self.generate_random_string(6) for _ in range(3)],
            "properties": {f"prop_{i}": self.generate_random_string(10) for i in range(5)}
        }
        
        return obj
    
    def calculate_object_size(self, obj: Dict[str, Any]) -> int:
        """计算JSON对象的字节大小"""
        return len(json.dumps(obj, ensure_ascii=False, separators=(',', ':')))
    
    def generate_json_file(self, target_size_mb: float, output_file: str) -> None:
        """
        生成指定大小的JSON文件
        
        Args:
            target_size_mb: 目标文件大小（MB）
            output_file: 输出文件路径
        """
        target_size_bytes = int(target_size_mb * 1024 * 1024)
        
        self.log(f"开始生成JSON文件，目标大小: {target_size_mb} MB ({target_size_bytes:,} 字节)")
        self.log(f"输出文件: {output_file}")
        
        print(f"开始生成JSON文件...")
        print(f"目标大小: {target_size_mb} MB ({target_size_bytes:,} 字节)")
        print(f"输出文件: {output_file}")
        
        # 首先生成一个示例对象来估算大小
        sample_obj = self.generate_sample_object()
        obj_size = self.calculate_object_size(sample_obj)
        
        # 估算需要生成的对象数量
        estimated_count = max(1, target_size_bytes // obj_size)
        
        self.log(f"单个对象大小约: {obj_size} 字节，估算需要生成: {estimated_count:,} 个对象")
        
        print(f"单个对象大小约: {obj_size} 字节")
        print(f"估算需要生成: {estimated_count:,} 个对象")
        
        # 生成数据
        data = {"items": []}
        current_size = len('{"items":[]}')  # 基础JSON结构的大小
        
        count = 0
        while current_size < target_size_bytes:
            obj = self.generate_sample_object()
            obj_json = json.dumps(obj, ensure_ascii=False, separators=(',', ':'))
            
            # 计算添加这个对象后的大小（包括逗号分隔符）
            additional_size = len(obj_json) + (1 if count > 0 else 0)  # +1 for comma
            
            if current_size + additional_size > target_size_bytes and count > 0:
                break
                
            data["items"].append(obj)
            current_size += additional_size
            count += 1
            
            if count % 1000 == 0:
                progress = (current_size / target_size_bytes) * 100
                self.log(f"生成进度: {progress:.1f}% ({count:,} 个对象, {current_size:,} 字节)")
                print(f"进度: {progress:.1f}% ({count:,} 个对象, {current_size:,} 字节)")
        
        # 写入文件
        self.log("开始写入文件到磁盘")
        print("正在写入文件...")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=None, separators=(',', ':'))
            self.log(f"文件写入成功: {output_file}")
        except Exception as e:
            self.log(f"文件写入失败: {e}", 'error')
            raise
        
        # 检查实际文件大小
        actual_size = os.path.getsize(output_file)
        actual_size_mb = actual_size / (1024 * 1024)
        
        self.log(f"文件生成完成! 实际大小: {actual_size_mb:.2f} MB，对象数量: {count:,}，平均对象大小: {actual_size // count if count > 0 else 0} 字节")
        
        print(f"\n文件生成完成!")
        print(f"实际文件大小: {actual_size_mb:.2f} MB ({actual_size:,} 字节)")
        print(f"生成对象数量: {count:,}")
        print(f"平均对象大小: {actual_size // count if count > 0 else 0} 字节")


def main():
    """主函数"""
    generator = JsonFileGenerator()
    
    print("=== JSON文件生成器 ===")
    
    try:
        # 获取用户输入
        size_input = input("请输入目标文件大小 (MB, 默认为1): ").strip()
        target_size = float(size_input) if size_input else 1.0
        
        filename_input = input("请输入输出文件名 (默认为generated_data.json): ").strip()
        filename = filename_input if filename_input else "generated_data.json"
        
        # 确保文件名有.json扩展名
        if not filename.endswith('.json'):
            filename += '.json'
        
        # 如果文件已存在，询问是否覆盖
        if os.path.exists(filename):
            overwrite = input(f"文件 '{filename}' 已存在，是否覆盖? (y/n): ").strip().lower()
            if overwrite not in ['y', 'yes', '是']:
                print("操作已取消。")
                return
        
        # 生成文件
        generator.log(f"开始执行文件生成任务，参数: 大小={target_size}MB, 文件名={filename}")
        generator.generate_json_file(target_size, filename)
        
    except ValueError as e:
        if 'generator' in locals():
            generator.log(f"输入值错误: {e}", 'error')
        print(f"错误: 无效的输入值 - {e}")
    except KeyboardInterrupt:
        if 'generator' in locals():
            generator.log("操作被用户中断", 'warning')
        print("\n操作被用户中断。")
    except Exception as e:
        if 'generator' in locals():
            generator.log(f"程序执行错误: {e}", 'error')
        print(f"发生错误: {e}")


if __name__ == "__main__":
    main()