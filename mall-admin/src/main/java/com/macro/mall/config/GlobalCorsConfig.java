package com.macro.mall.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

/**
 * 全局跨域配置类
 * 用于配置允许哪些跨域请求可以访问后端服务
 * 创建者：macro，创建时间：2019/7/27
 */
@Configuration
public class GlobalCorsConfig {

    /**
     * 创建并返回一个允许跨域访问的过滤器
     * @return CorsFilter 跨域过滤器
     */
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        // 允许所有域名进行跨域访问
        config.addAllowedOriginPattern("*");
        // 允许跨域请求携带 Cookie 信息
        config.setAllowCredentials(true);
        // 允许所有请求头信息
        config.addAllowedHeader("*");
        // 允许所有的 HTTP 请求方法（GET、POST、PUT、DELETE 等）
        config.addAllowedMethod("*");
        // 创建基于 URL 的跨域配置源对象
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        // 对所有 URL 应用上述跨域配置
        source.registerCorsConfiguration("/**", config);
        // 返回配置好的跨域过滤器
        return new CorsFilter(source);
    }
}
