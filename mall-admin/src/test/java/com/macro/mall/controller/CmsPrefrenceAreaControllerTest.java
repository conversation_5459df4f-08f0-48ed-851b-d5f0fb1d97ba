package com.macro.mall.controller;

import com.macro.mall.common.api.CommonResult;
import com.macro.mall.model.CmsPrefrenceArea;
import com.macro.mall.service.CmsPrefrenceAreaService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit.jupiter.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * CmsPrefrenceAreaController 单元测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class CmsPrefrenceAreaControllerTest {

    @Mock
    private CmsPrefrenceAreaService prefrenceAreaService;

    @InjectMocks
    private CmsPrefrenceAreaController cmsPrefrenceAreaController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(cmsPrefrenceAreaController).build();
    }

    /**
     * 测试获取所有商品优选 - 成功返回数据
     */
    @Test
    void testListAll_Success() throws Exception {
        // 准备测试数据
        List<CmsPrefrenceArea> mockPrefrenceAreaList = createMockPrefrenceAreaList();
        
        // Mock service 方法
        when(prefrenceAreaService.listAll()).thenReturn(mockPrefrenceAreaList);

        // 执行请求并验证结果
        mockMvc.perform(get("/prefrenceArea/listAll")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].name").value("数码产品"))
                .andExpect(jsonPath("$.data[0].subTitle").value("精选数码产品"))
                .andExpect(jsonPath("$.data[0].sort").value(1))
                .andExpect(jsonPath("$.data[0].showStatus").value(1))
                .andExpect(jsonPath("$.data[1].id").value(2))
                .andExpect(jsonPath("$.data[1].name").value("家居用品"))
                .andExpect(jsonPath("$.data[1].subTitle").value("精选家居用品"))
                .andExpect(jsonPath("$.data[1].sort").value(2))
                .andExpect(jsonPath("$.data[1].showStatus").value(1));

        // 验证service方法被调用了一次
        verify(prefrenceAreaService, times(1)).listAll();
    }

    /**
     * 测试获取所有商品优选 - 返回空列表
     */
    @Test
    void testListAll_EmptyList() throws Exception {
        // Mock service 返回空列表
        when(prefrenceAreaService.listAll()).thenReturn(new ArrayList<>());

        // 执行请求并验证结果
        mockMvc.perform(get("/prefrenceArea/listAll")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));

        // 验证service方法被调用了一次
        verify(prefrenceAreaService, times(1)).listAll();
    }

    /**
     * 测试获取所有商品优选 - Service层抛出异常
     */
    @Test
    void testListAll_ServiceThrowsException() throws Exception {
        // Mock service 抛出异常
        when(prefrenceAreaService.listAll()).thenThrow(new RuntimeException("数据库连接错误"));

        // 执行请求并验证异常处理
        mockMvc.perform(get("/prefrenceArea/listAll")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isInternalServerError());

        // 验证service方法被调用了一次
        verify(prefrenceAreaService, times(1)).listAll();
    }

    /**
     * 测试 HTTP 方法不匹配 - POST方法不被支持
     */
    @Test
    void testListAll_UnsupportedHttpMethod() throws Exception {
        mockMvc.perform(post("/prefrenceArea/listAll")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isMethodNotAllowed());

        // 验证service方法没有被调用
        verify(prefrenceAreaService, never()).listAll();
    }

    /**
     * 创建模拟的商品优选区域列表
     */
    private List<CmsPrefrenceArea> createMockPrefrenceAreaList() {
        CmsPrefrenceArea area1 = new CmsPrefrenceArea();
        area1.setId(1L);
        area1.setName("数码产品");
        area1.setSubTitle("精选数码产品");
        area1.setSort(1);
        area1.setShowStatus(1);
        area1.setPic("digital_pic".getBytes());

        CmsPrefrenceArea area2 = new CmsPrefrenceArea();
        area2.setId(2L);
        area2.setName("家居用品");
        area2.setSubTitle("精选家居用品");
        area2.setSort(2);
        area2.setShowStatus(1);
        area2.setPic("home_pic".getBytes());

        return Arrays.asList(area1, area2);
    }
}