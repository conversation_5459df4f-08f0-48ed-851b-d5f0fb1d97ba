# Mall-Demo 模块目录结构设计文档

## 概述

Mall-Demo 是商城系统的演示模块，主要用于展示系统的基本功能和架构设计。该模块采用典型的Spring Boot项目结构，包含了商城系统的核心组件示例。

## 目录结构

```
mall-demo/
├── .gitignore                          # Git忽略文件配置
├── pom.xml                            # Maven项目配置文件
└── src/
    └── main/
        ├── java/
        │   └── com/macro/mall/demo/
        │       ├── MallDemoApplication.java    # Spring Boot启动类
        │       ├── bo/                         # 业务对象层
        │       ├── config/                     # 配置类目录
        │       ├── controller/                 # 控制器层
        │       ├── dto/                        # 数据传输对象
        │       ├── service/                    # 服务层
        │       └── validator/                  # 验证器
        └── resources/
            └── application.yml                 # 应用配置文件
```

## 架构分层

### 1. 启动类
- **MallDemoApplication.java**: Spring Boot应用程序入口点

### 2. 业务对象层 (BO - Business Object)
- 包含业务逻辑相关的对象定义
- 用于封装复杂的业务规则和数据操作

### 3. 配置层 (Config)
- 包含4个配置文件
- 负责应用程序的各种配置设置
- 包括数据库配置、安全配置、Web配置等

### 4. 控制器层 (Controller)
- 包含2个控制器
- 处理HTTP请求和响应
- 作为前端与后端服务的接口层

### 5. 数据传输对象 (DTO - Data Transfer Object)
- 用于在不同层之间传输数据
- 封装请求和响应的数据结构

### 6. 服务层 (Service)
- 包含2个服务类
- 实现核心业务逻辑
- 提供事务管理和业务规则执行

### 7. 验证器 (Validator)
- 包含2个验证器
- 负责数据验证和业务规则校验
- 确保数据的完整性和有效性

## 设计特点

### 分层架构
采用经典的MVC三层架构模式：
- **Controller层**: 负责请求处理和路由
- **Service层**: 负责业务逻辑实现
- **DTO层**: 负责数据传输和转换

### 职责分离
- **BO**: 业务对象，封装业务逻辑
- **DTO**: 数据传输，简化数据交换
- **Validator**: 数据验证，保证数据质量
- **Config**: 配置管理，集中配置设置

### 模块化设计
每个功能模块都有清晰的边界和职责，便于：
- 代码维护和扩展
- 单元测试的编写
- 团队协作开发

## 技术栈

- **框架**: Spring Boot
- **构建工具**: Maven
- **配置格式**: YAML
- **包结构**: 标准Java包命名规范 (com.macro.mall.demo)

## 配置文件

### application.yml
位于 `src/main/resources/` 目录下，包含应用程序的核心配置：
- 数据库连接配置
- 服务端口配置
- 日志级别配置
- 其他应用程序属性

## 开发规范

### 包命名规范
- 使用公司域名倒序 + 项目名称 + 模块名称
- 示例：`com.macro.mall.demo`

### 文件组织原则
- 按功能模块组织代码
- 相同类型的类放在同一个包下
- 保持包结构的清晰和一致性

### 代码分层原则
- Controller只处理HTTP请求响应
- Service层实现具体业务逻辑
- DTO用于数据传输，不包含业务逻辑
- Validator专门负责数据校验

## 扩展性考虑

当前目录结构支持：
- 新功能模块的快速添加
- 配置的灵活管理
- 业务逻辑的独立开发
- 数据验证规则的统一管理

这种设计为未来的功能扩展和系统集成提供了良好的基础架构。