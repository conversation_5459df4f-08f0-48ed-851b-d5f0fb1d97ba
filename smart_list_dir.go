package main

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

// 配置常量
const (
	FILE_THRESHOLD    = 25 // 文件数量阈值，超过则使用摘要模式
	MAX_DISPLAY_DEPTH = 5  // 最大展示深度
	INDENT_SIZE       = 2  // 缩进空格数
)

// DirEntry 表示一个目录项
type DirEntry struct {
	Name       string
	Path       string
	IsDir      bool
	Children   []*DirEntry
	FileCount  int            // 子树中的文件总数
	FileTypes  map[string]int // 文件类型统计
	IsSummary  bool           // 是否使用摘要模式
	IsEllipsis bool           // 是否使用省略号
}

// DisplayOptions 控制展示选项
type DisplayOptions struct {
	CurrentDepth   int
	MaxDepth       int
	IgnorePatterns []string // gitignore 模式
	RootPath       string
	ShowHidden     bool
}

// DirClassifier 目录分类器
type DirClassifier struct {
	// 编译输出目录
	BuildDirs map[string]bool
	// 依赖目录
	DependencyDirs map[string]bool
	// 源代码目录
	SourceDirs map[string]bool
	// 关键文件
	ImportantFiles map[string]bool
}

func NewDirClassifier() *DirClassifier {
	return &DirClassifier{
		BuildDirs: map[string]bool{
			"target":       true,
			"build":        true,
			"dist":         true,
			"out":          true,
			".next":        true,
			"node_modules": true,
		},
		DependencyDirs: map[string]bool{
			"node_modules": true,
			"vendor":       true,
			".gradle":      true,
			".m2":          true,
		},
		SourceDirs: map[string]bool{
			"src":       true,
			"lib":       true,
			"main":      true,
			"java":      true,
			"resources": true,
		},
		ImportantFiles: map[string]bool{
			"pom.xml":                true,
			"package.json":           true,
			"README.md":              true,
			"Dockerfile":             true,
			"docker-compose.yml":     true,
			"application.yml":        true,
			"application.properties": true,
			".gitignore":             true,
		},
	}
}

// SmartListDir 主结构体
type SmartListDir struct {
	classifier *DirClassifier
	gitignore  *GitignoreParser
}

func NewSmartListDir() *SmartListDir {
	return &SmartListDir{
		classifier: NewDirClassifier(),
		gitignore:  NewGitignoreParser(),
	}
}

// List 列出目录内容
func (s *SmartListDir) List(path string) (*DirEntry, error) {
	// 加载 gitignore 规则
	s.gitignore.LoadFromPath(path)

	opts := &DisplayOptions{
		CurrentDepth: 0,
		MaxDepth:     MAX_DISPLAY_DEPTH,
		RootPath:     path,
		ShowHidden:   false,
	}

	return s.buildDirTree(path, opts)
}

// buildDirTree 构建目录树
func (s *SmartListDir) buildDirTree(path string, opts *DisplayOptions) (*DirEntry, error) {
	info, err := os.Stat(path)
	if err != nil {
		return nil, err
	}

	entry := &DirEntry{
		Name:  filepath.Base(path),
		Path:  path,
		IsDir: info.IsDir(),
	}

	if !info.IsDir() {
		return entry, nil
	}

	// 读取目录内容
	files, err := os.ReadDir(path)
	if err != nil {
		return entry, err
	}

	// 过滤和分类
	var visibleFiles []os.DirEntry
	for _, file := range files {
		if s.shouldSkip(file, path, opts) {
			continue
		}
		visibleFiles = append(visibleFiles, file)
	}

	// 决定展示策略
	strategy := s.determineStrategy(path, visibleFiles, opts)

	switch strategy {
	case "ellipsis":
		entry.IsEllipsis = true
	case "summary":
		entry.IsSummary = true
		s.buildSummary(entry, path, visibleFiles)
	case "full":
		s.buildFullTree(entry, path, visibleFiles, opts)
	case "partial":
		s.buildPartialTree(entry, path, visibleFiles, opts)
	}

	return entry, nil
}

// determineStrategy 决定展示策略
func (s *SmartListDir) determineStrategy(path string, files []os.DirEntry, opts *DisplayOptions) string {
	dirName := filepath.Base(path)

	// 1. 检查是否是依赖目录
	if s.classifier.DependencyDirs[dirName] {
		return "ellipsis"
	}

	// 2. 检查深度
	if opts.CurrentDepth >= opts.MaxDepth {
		return "ellipsis"
	}

	// 3. 检查是否是编译输出目录
	if s.classifier.BuildDirs[dirName] {
		if opts.CurrentDepth > 2 {
			return "ellipsis"
		}
		return "partial" // 显示关键文件
	}

	// 4. 检查文件数量
	fileCount := s.countFiles(files)
	if fileCount > FILE_THRESHOLD {
		// 源代码目录即使文件多也要显示结构
		if s.classifier.SourceDirs[dirName] && opts.CurrentDepth < 3 {
			return "summary"
		}
		return "summary"
	}

	// 5. 默认完整显示
	return "full"
}

// buildSummary 构建摘要信息
func (s *SmartListDir) buildSummary(entry *DirEntry, path string, files []os.DirEntry) {
	entry.FileTypes = make(map[string]int)

	// 递归统计所有文件
	filepath.Walk(path, func(p string, info os.FileInfo, err error) error {
		if err != nil || info.IsDir() {
			return nil
		}

		// 跳过 gitignore 的文件
		if s.gitignore.IsIgnored(p) {
			return nil
		}

		entry.FileCount++
		ext := filepath.Ext(p)
		if ext == "" {
			ext = "no-ext"
		}
		entry.FileTypes[ext]++
		return nil
	})
}

// buildFullTree 构建完整目录树
func (s *SmartListDir) buildFullTree(entry *DirEntry, path string, files []os.DirEntry, opts *DisplayOptions) {
	for _, file := range files {
		filePath := filepath.Join(path, file.Name())

		if file.IsDir() {
			newOpts := *opts
			newOpts.CurrentDepth++
			child, _ := s.buildDirTree(filePath, &newOpts)
			if child != nil {
				entry.Children = append(entry.Children, child)
			}
		} else {
			entry.Children = append(entry.Children, &DirEntry{
				Name:  file.Name(),
				Path:  filePath,
				IsDir: false,
			})
		}
	}
}

// buildPartialTree 构建部分目录树（只显示重要文件）
func (s *SmartListDir) buildPartialTree(entry *DirEntry, path string, files []os.DirEntry, opts *DisplayOptions) {
	var importantItems []*DirEntry
	var hasMoreContent bool

	for _, file := range files {
		fileName := file.Name()
		filePath := filepath.Join(path, fileName)

		// 检查是否是重要文件或目录
		if s.classifier.ImportantFiles[fileName] ||
			(file.IsDir() && s.classifier.SourceDirs[fileName]) {
			if file.IsDir() {
				// 对重要目录使用省略号
				importantItems = append(importantItems, &DirEntry{
					Name:       fileName,
					Path:       filePath,
					IsDir:      true,
					IsEllipsis: true,
				})
			} else {
				importantItems = append(importantItems, &DirEntry{
					Name:  fileName,
					Path:  filePath,
					IsDir: false,
				})
			}
		} else {
			hasMoreContent = true
		}
	}

	entry.Children = importantItems

	// 如果有更多内容，添加省略号指示
	if hasMoreContent {
		entry.Children = append(entry.Children, &DirEntry{
			Name:       "...",
			IsEllipsis: true,
		})
	}
}

// shouldSkip 判断是否应该跳过文件
func (s *SmartListDir) shouldSkip(file os.DirEntry, parentPath string, opts *DisplayOptions) bool {
	fileName := file.Name()

	// 1. 隐藏文件
	if !opts.ShowHidden && strings.HasPrefix(fileName, ".") {
		return true
	}

	// 2. gitignore 规则
	filePath := filepath.Join(parentPath, fileName)
	if s.gitignore.IsIgnored(filePath) {
		return true
	}

	return false
}

// countFiles 统计文件数量（不递归）
func (s *SmartListDir) countFiles(files []os.DirEntry) int {
	count := 0
	for _, file := range files {
		if !file.IsDir() {
			count++
		}
	}
	return count
}

// Render 渲染输出
func (s *SmartListDir) Render(entry *DirEntry, indent int) string {
	var result strings.Builder
	prefix := strings.Repeat(" ", indent*INDENT_SIZE)

	// 渲染当前项
	if entry.IsEllipsis {
		result.WriteString(fmt.Sprintf("%s- %s/...\n", prefix, entry.Name))
		return result.String()
	}

	if entry.IsDir {
		result.WriteString(fmt.Sprintf("%s- %s/\n", prefix, entry.Name))

		if entry.IsSummary {
			// 渲染摘要
			summaryPrefix := strings.Repeat(" ", (indent+1)*INDENT_SIZE)
			summary := s.formatSummary(entry)
			result.WriteString(fmt.Sprintf("%s%s\n", summaryPrefix, summary))
		} else {
			// 渲染子项
			for _, child := range entry.Children {
				result.WriteString(s.Render(child, indent+1))
			}
		}
	} else {
		result.WriteString(fmt.Sprintf("%s- %s\n", prefix, entry.Name))
	}

	return result.String()
}

// formatSummary 格式化摘要信息
func (s *SmartListDir) formatSummary(entry *DirEntry) string {
	if entry.FileCount == 0 {
		return "[empty]"
	}

	// 构建文件类型统计字符串
	var types []string
	for ext, count := range entry.FileTypes {
		if ext == "no-ext" {
			types = append(types, fmt.Sprintf("%d files", count))
		} else {
			types = append(types, fmt.Sprintf("%d *%s", count, ext))
		}
	}

	return fmt.Sprintf("[%d files in subtree: %s]",
		entry.FileCount, strings.Join(types, ", "))
}

// GitignoreParser 简化的 gitignore 解析器
type GitignoreParser struct {
	patterns []string
}

func NewGitignoreParser() *GitignoreParser {
	return &GitignoreParser{
		patterns: []string{},
	}
}

func (g *GitignoreParser) LoadFromPath(path string) {
	// 实际实现中需要递归查找 .gitignore 文件并解析
	// 这里简化处理，只添加一些常见的忽略模式
	g.patterns = []string{
		"*.log",
		"*.tmp",
		".git",
		".DS_Store",
		"thumbs.db",
	}
}

func (g *GitignoreParser) IsIgnored(path string) bool {
	// 简化的匹配逻辑
	fileName := filepath.Base(path)
	for _, pattern := range g.patterns {
		if matched, _ := filepath.Match(pattern, fileName); matched {
			return true
		}
	}
	return false
}

// 主函数示例
func main() {
	lister := NewSmartListDir()

	// 示例：列出当前目录
	if entry, err := lister.List("."); err == nil {
		output := lister.Render(entry, 0)
		fmt.Println(output)

		// 添加文件统计信息
		fmt.Println("\nNote: File extension counts do not include files ignored by .gitignore.")
	}
}
